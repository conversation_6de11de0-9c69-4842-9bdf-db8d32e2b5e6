const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const { errorHandler } = require('./utils/errors');
const authMiddleware = require('./middleware/auth');
const rateLimitMiddleware = require('./middleware/rateLimit');

// Import routes
const receiptsRouter = require('./routes/receipts');
const imagesRouter = require('./routes/images');
const healthRouter = require('./routes/health');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());
app.use(cors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['*'],
    credentials: true
}));

// Logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Rate limiting
app.use(rateLimitMiddleware);

// Health check (no auth required)
app.use('/api/v1/health', healthRouter);

// Protected routes (require authentication)
app.use('/api/v1/receipts', authMiddleware, receiptsRouter);
app.use('/api/v1/images', authMiddleware, imagesRouter);

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'SpendSmart Backend API',
        version: '1.0.0',
        status: 'running'
    });
});

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found'
    });
});

app.listen(PORT, () => {
    console.log(`🚀 SpendSmart Backend running on port ${PORT}`);
    console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
});

module.exports = app;