const Joi = require('joi');
const { AppError } = require('../utils/errors');

const validateReceiptProcessing = (req, res, next) => {
    const schema = Joi.object({
        useOpenAI: Joi.string().valid('true', 'false').optional()
    });

    const { error } = schema.validate(req.body);
    if (error) {
        throw new AppError(error.details[0].message, 400);
    }

    next();
};

const validateImageUpload = (req, res, next) => {
    // Basic validation - file presence is checked in route handler
    if (!req.file) {
        throw new AppError('Image file is required', 400);
    }

    // Check file size (additional check beyond multer)
    if (req.file.size > 10 * 1024 * 1024) {
        throw new AppError('Image file too large (max 10MB)', 400);
    }

    next();
};

module.exports = {
    validateReceiptProcessing,
    validateImageUpload
};