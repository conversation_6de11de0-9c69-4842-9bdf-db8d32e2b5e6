const rateLimit = require('express-rate-limit');

// Different rate limits for different endpoints
const createRateLimit = (windowMs, max, message) => {
    return rateLimit({
        windowMs,
        max,
        message: {
            success: false,
            error: message
        },
        standardHeaders: true,
        legacyHeaders: false,
    });
};

// General rate limit
const generalLimit = createRateLimit(
    15 * 60 * 1000, // 15 minutes
    100, // limit each IP to 100 requests per windowMs
    'Too many requests from this IP, please try again later.'
);

// Stricter limit for AI processing endpoints
const aiProcessingLimit = createRateLimit(
    60 * 1000, // 1 minute
    10, // limit each IP to 10 AI requests per minute
    'Too many AI processing requests, please try again later.'
);

// Image upload limit
const imageUploadLimit = createRateLimit(
    60 * 1000, // 1 minute
    20, // limit each IP to 20 image uploads per minute
    'Too many image upload requests, please try again later.'
);

module.exports = {
    general: generalLimit,
    aiProcessing: aiProcessingLimit,
    imageUpload: imageUploadLimit
};