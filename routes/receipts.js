const express = require('express');
const multer = require('multer');
const { aiProcessing: aiProcessingLimit } = require('../middleware/rateLimit');
const { validateReceiptProcessing } = require('../middleware/validation');
const aiService = require('../services/aiService');
const { AppError } = require('../utils/errors');

const router = express.Router();

// Configure multer for image uploads
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new AppError('Only image files are allowed', 400), false);
        }
    }
});

// POST /api/v1/receipts/process
router.post('/process',
    aiProcessingLimit,
    upload.single('image'),
    validateReceiptProcessing,
    async (req, res, next) => {
        try {
            if (!req.file) {
                throw new AppError('No image file provided', 400);
            }

            const { useOpenAI = 'true' } = req.body;
            const shouldUseOpenAI = useOpenAI === 'true';

            console.log(`🤖 Processing receipt with ${shouldUseOpenAI ? 'OpenAI' : 'Gemini'} for user: ${req.user.id}`);

            // Process the receipt using AI service
            const result = await aiService.processReceipt(req.file.buffer, shouldUseOpenAI);

            // Log successful processing
            console.log(`✅ Receipt processed successfully for user: ${req.user.id}`);

            res.json({
                success: true,
                data: result
            });

        } catch (error) {
            console.error('Receipt processing error:', error);
            next(error);
        }
    }
);

module.exports = router;