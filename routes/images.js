const express = require('express');
const multer = require('multer');
const { imageUploadLimit } = require('../middleware/rateLimit');
const { validateImageUpload } = require('../middleware/validation');
const imageStorageService = require('../services/imageStorage');
const { AppError } = require('../utils/errors');

const router = express.Router();

// Configure multer for image uploads
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new AppError('Only image files are allowed', 400), false);
        }
    }
});

// POST /api/v1/images/upload
router.post('/upload',
    imageUploadLimit,
    upload.single('image'),
    validateImageUpload,
    async (req, res, next) => {
        try {
            if (!req.file) {
                throw new AppError('No image file provided', 400);
            }

            console.log(`📸 Uploading image for user: ${req.user.id}`);

            // Upload image using ImgBB service
            const imageUrl = await imageStorageService.uploadImage(req.file.buffer, req.file.originalname);

            console.log(`✅ Image uploaded successfully for user: ${req.user.id}`);

            res.json({
                success: true,
                data: {
                    url: imageUrl
                }
            });

        } catch (error) {
            console.error('Image upload error:', error);
            next(error);
        }
    }
);

module.exports = router;