const axios = require('axios');
const FormData = require('form-data');
const { AppError } = require('../utils/errors');

class ImageStorageService {
    constructor() {
        this.imgbbKeys = [
            process.env.IMGBB_API_KEY,
            process.env.IMGBB_API_KEY_2,
            process.env.IMGBB_API_KEY_3
        ].filter(Boolean);
        this.currentKeyIndex = 0;
    }

    async uploadImage(imageBuffer, filename = 'receipt.jpg') {
        let lastError;

        // Try each ImgBB API key
        for (let i = 0; i < this.imgbbKeys.length; i++) {
            const keyIndex = (this.currentKeyIndex + i) % this.imgbbKeys.length;
            const apiKey = this.imgbbKeys[keyIndex];

            try {
                console.log(`🔑 Trying ImgBB upload with key ${keyIndex + 1} of ${this.imgbbKeys.length}`);

                const formData = new FormData();
                formData.append('image', imageBuffer.toString('base64'));
                formData.append('name', filename);

                const response = await axios.post(`https://api.imgbb.com/1/upload?key=${apiKey}`, formData, {
                    headers: {
                        ...formData.getHeaders(),
                    },
                    timeout: 30000, // 30 second timeout
                });

                if (response.data && response.data.success && response.data.data && response.data.data.url) {
                    console.log(`✅ ImgBB upload successful with key ${keyIndex + 1}`);
                    this.currentKeyIndex = keyIndex; // Update successful key index
                    return response.data.data.url;
                } else {
                    throw new Error('Invalid response from ImgBB');
                }
            } catch (error) {
                console.error(`ImgBB key ${keyIndex + 1} failed:`, error.message);
                lastError = error;
                continue;
            }
        }

        console.error('All ImgBB API keys failed');
        throw new AppError('Failed to upload image to storage service', 500);
    }
}

module.exports = new ImageStorageService();