const OpenAI = require('openai');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const { AppError } = require('../utils/errors');

class AIService {
    constructor() {
        // Initialize OpenAI
        this.openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
        });

        // Initialize Gemini
        this.gemini = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        this.geminiModel = this.gemini.getGenerativeModel({ model: 'gemini-2.0-flash' });

        // API key rotation for Gemini
        this.geminiKeys = [
            process.env.GEMINI_API_KEY,
            process.env.GEMINI_API_KEY_2
        ].filter(Boolean);
        this.currentGeminiKeyIndex = 0;
    }

    async processReceipt(imageBuffer, useOpenAI = true) {
        const systemPrompt = `### **SpendSmart Receipt Extraction and Validation System**

#### **CRITICAL: Receipt Validation First**
Before extracting any data, you MUST validate if this image contains a valid receipt:
- A valid receipt should have: store name, date, items with prices, and total amount
- If the image is blurry, unclear, not a receipt, or missing critical elements, return validation failure
- If validation fails, return ONLY: {"isValid": false, "message": "Invalid receipt detected"}
- If validation passes, proceed with full data extraction

#### **Data Extraction Requirements**
Extract the following information from the receipt:
1. **Store Information**: Name, address, phone number
2. **Transaction Details**: Date, time, receipt number
3. **Items**: Name, price, category, discounts
4. **Totals**: Subtotal, tax, total amount
5. **Payment**: Method used
6. **Logo Search Term**: Generic brand name for logo search (e.g., "walmart", "target", "starbucks")

#### **Discount Handling**
- Identify discounts, free items, and points redemptions
- Mark discount items with isDiscount: true
- Include originalPrice for discounted items
- Points redemptions should have price: 0.0 and isDiscount: true

#### **Response Format**
Return ONLY valid JSON with this exact structure:
{
  "isValid": true,
  "store_name": "Store Name",
  "store_address": "Full Address",
  "receipt_name": "Receipt from Store Name",
  "purchase_date": "2024-01-15T10:30:00Z",
  "currency": "USD",
  "payment_method": "Credit Card",
  "total_amount": 25.99,
  "total_tax": 2.08,
  "logo_search_term": "storename",
  "items": [
    {
      "id": "uuid",
      "name": "Item Name",
      "price": 12.99,
      "category": "Food",
      "originalPrice": 15.99,
      "discountDescription": "Store Discount",
      "isDiscount": false
    }
  ]
}`;

        const prompt = "Extract all receipt details from this image and return in the specified JSON format.";

        try {
            if (useOpenAI) {
                return await this.processWithOpenAI(imageBuffer, prompt, systemPrompt);
            } else {
                return await this.processWithGemini(imageBuffer, prompt, systemPrompt);
            }
        } catch (error) {
            console.error('AI processing failed:', error);
            throw new AppError('Failed to process receipt with AI service', 500);
        }
    }

    async processWithOpenAI(imageBuffer, prompt, systemPrompt) {
        try {
            const base64Image = imageBuffer.toString('base64');
            const dataURL = `data:image/jpeg;base64,${base64Image}`;

            const response = await this.openai.chat.completions.create({
                model: 'gpt-4o-mini',
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt
                    },
                    {
                        role: 'user',
                        content: [
                            { type: 'text', text: prompt },
                            { type: 'image_url', image_url: { url: dataURL, detail: 'auto' } }
                        ]
                    }
                ],
                max_tokens: 2048,
                temperature: 0.2
            });

            const content = response.choices[0]?.message?.content;
            if (!content) {
                throw new AppError('No response from OpenAI', 500);
            }

            // Log cost information
            const usage = response.usage;
            if (usage) {
                const inputCost = (usage.prompt_tokens / 1000) * 0.00015; // $0.00015 per 1K input tokens
                const outputCost = (usage.completion_tokens / 1000) * 0.0006; // $0.0006 per 1K output tokens
                const totalCost = inputCost + outputCost;

                console.log(`💰 OpenAI Cost: $${totalCost.toFixed(6)} (Input: ${usage.prompt_tokens}, Output: ${usage.completion_tokens})`);
            }

            return this.parseAIResponse(content);
        } catch (error) {
            console.error('OpenAI processing error:', error);
            throw error;
        }
    }

    async processWithGemini(imageBuffer, prompt, systemPrompt) {
        let lastError;

        // Try each Gemini API key
        for (let i = 0; i < this.geminiKeys.length; i++) {
            const keyIndex = (this.currentGeminiKeyIndex + i) % this.geminiKeys.length;
            const apiKey = this.geminiKeys[keyIndex];

            try {
                const gemini = new GoogleGenerativeAI(apiKey);
                const model = gemini.getGenerativeModel({
                    model: 'gemini-2.0-flash',
                    systemInstruction: systemPrompt
                });

                const imagePart = {
                    inlineData: {
                        data: imageBuffer.toString('base64'),
                        mimeType: 'image/jpeg'
                    }
                };

                const response = await model.generateContent([prompt, imagePart]);
                const content = response.response.text();

                if (!content) {
                    throw new AppError('No response from Gemini', 500);
                }

                console.log(`✅ Gemini processing successful with key ${keyIndex + 1}`);
                this.currentGeminiKeyIndex = keyIndex; // Update successful key index

                return this.parseAIResponse(content);
            } catch (error) {
                console.error(`Gemini key ${keyIndex + 1} failed:`, error);
                lastError = error;
                continue;
            }
        }

        throw lastError || new AppError('All Gemini API keys failed', 500);
    }

    parseAIResponse(content) {
        try {
            // Clean the response to ensure it's valid JSON
            let cleanedContent = content.trim();

            // Remove markdown code blocks if present
            if (cleanedContent.startsWith('```json')) {
                cleanedContent = cleanedContent.replace(/```json\n?/, '').replace(/\n?```$/, '');
            } else if (cleanedContent.startsWith('```')) {
                cleanedContent = cleanedContent.replace(/```\n?/, '').replace(/\n?```$/, '');
            }

            const parsed = JSON.parse(cleanedContent);

            // Validate required fields
            if (typeof parsed.isValid !== 'boolean') {
                throw new AppError('Invalid AI response: missing isValid field', 500);
            }

            return parsed;
        } catch (error) {
            console.error('Failed to parse AI response:', error);
            console.error('Raw content:', content);
            throw new AppError('Failed to parse AI response', 500);
        }
    }
}

module.exports = new AIService();